
import React from 'react';
import { EducationTrainingItem as EducationTrainingType, RepeatableSectionProps } from '../../../types'; // Renamed import
import Input from '../../common/Input';
import Textarea from '../../common/Textarea';
import RepeatableFieldSection, { ItemActions } from '../../common/RepeatableFieldSection';
import { v4 as uuidv4 } from 'uuid';
import { useLanguage } from '../../../contexts/LanguageContext';

const createEmptyEducationTrainingItem = (): EducationTrainingType => ({ // Renamed function
  id: uuidv4(),
  startDate: '',
  endDate: '',
  current: false,
  titleOfQualificationAwarded: '',
  organisationName: '',
  organisationAddress: '',
  mainSubjectsOrSkillsCovered: '',
  levelInNationalOrInternationalClassification: '',
});

const EducationTrainingItemComponent: React.FC<{ // Renamed component
  item: EducationTrainingType;
  onChange: (updatedItem: EducationTrainingType) => void;
  onRemove: (id: string) => void;
}> = ({ item, onChange, onRemove }) => {
  const { t } = useLanguage();
  const handleChange = (field: keyof EducationTrainingType, value: any) => {
    onChange({ ...item, [field]: value });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative pb-8">
      <ItemActions onRemove={() => onRemove(item.id)} />
      <Input 
        label={t('educationSection.titleOfQualificationAwarded')} 
        value={item.titleOfQualificationAwarded} 
        onChange={e => handleChange('titleOfQualificationAwarded', e.target.value)} 
        required 
        containerClassName="md:col-span-2"
      />
      <Input label={t('educationSection.organisationName')} value={item.organisationName} onChange={e => handleChange('organisationName', e.target.value)} required />
      <Input label={t('educationSection.organisationAddress')} value={item.organisationAddress || ''} onChange={e => handleChange('organisationAddress', e.target.value)} />
      
      <Input label={t('educationSection.startDate')} type="month" value={item.startDate} onChange={e => handleChange('startDate', e.target.value)} required />
       <div>
        <Input label={t('educationSection.endDate')} type="month" value={item.endDate} onChange={e => handleChange('endDate', e.target.value)} disabled={item.current} />
        <label className="flex items-center mt-1">
          <input type="checkbox" checked={item.current} onChange={e => handleChange('current', e.target.checked)} className="mr-2 h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500" />
          {t('educationSection.currentlyStudying')}
        </label>
      </div>
      <div className="md:col-span-2">
        <Textarea 
            label={t('educationSection.mainSubjectsOrSkillsCovered')} 
            value={item.mainSubjectsOrSkillsCovered} 
            onChange={e => handleChange('mainSubjectsOrSkillsCovered', e.target.value)} 
            rows={3}
            placeholder="List main subjects or skills covered"
        />
      </div>
      <div className="md:col-span-2">
        <Input 
            label={t('educationSection.levelInClassification')} 
            value={item.levelInNationalOrInternationalClassification || ''} 
            onChange={e => handleChange('levelInNationalOrInternationalClassification', e.target.value)} 
            placeholder="e.g. EQF Level 5, Bachelor degree"
        />
      </div>
    </div>
  );
};

const EducationTrainingSection: React.FC<Omit<RepeatableSectionProps<EducationTrainingType>, 'renderItem' | 'createEmptyItem' | 'title'>> = (props) => {
  const { t } = useLanguage();
  return (
    <RepeatableFieldSection
      {...props}
      title={t('cvForm.educationTrainingTitle')} // Ensure this key exists and is correct
      addItemText={t('educationSection.addItem')}
      createEmptyItem={createEmptyEducationTrainingItem}
      renderItem={(item, index, onChange, onRemove) => (
        <EducationTrainingItemComponent item={item} onChange={onChange} onRemove={onRemove} />
      )}
    />
  );
};

export default EducationTrainingSection;
