import React from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import DashboardPage from './pages/DashboardPage';
import CVEditorPage from './pages/CVEditorPage';
import PDFPreviewPage from './pages/PDFPreviewPage';
import { CVProvider } from './contexts/CVContext';
import { useLanguage } from './contexts/LanguageContext';
import { LanguageCode } from './types';
import Button from './components/common/Button';


const App: React.FC = () => {
  const { language, setLanguage, t } = useLanguage();

  const handleLanguageChange = (langCode: LanguageCode) => {
    setLanguage(langCode);
  };

  return (
    <CVProvider>
      <HashRouter>
        <div className="min-h-screen flex flex-col">
          <header className="bg-primary-700 text-white p-4 shadow-md">
            <div className="container mx-auto flex flex-col sm:flex-row justify-between items-center">
              <h1 className="text-2xl font-bold mb-2 sm:mb-0">{t('headerTitle')}</h1>
              <div className="flex items-center space-x-2">
                <span className="text-sm">{t('languageSelector')}</span>
                {(['en', 'it'] as LanguageCode[]).map((langCode) => (
                  <Button
                    key={langCode}
                    onClick={() => handleLanguageChange(langCode)}
                    variant={language === langCode ? 'primary' : 'outline'}
                    size="sm"
                    className={`
                      ${language === langCode ? 'bg-white text-primary-700' : 'border-white text-white hover:bg-white hover:text-primary-700'}
                      disabled:opacity-100
                    `} // Adjust active/inactive styles for header
                  >
                    {t(`languages.${langCode}`)}
                  </Button>
                ))}
              </div>
            </div>
          </header>
          <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8">
            <Routes>
              <Route path="/" element={<DashboardPage />} />
              <Route path="/new" element={<CVEditorPage />} />
              <Route path="/edit/:cvId" element={<CVEditorPage />} />
              <Route path="/preview/:cvId" element={<PDFPreviewPage />} />
              <Route path="*" element={<Navigate to="/" />} />
            </Routes>
          </main>
          <footer className="bg-gray-800 text-white text-center p-4 mt-auto">
            <p>{t('footerMessage', { year: new Date().getFullYear() })}</p>
          </footer>
        </div>
      </HashRouter>
    </CVProvider>
  );
};

export default App;