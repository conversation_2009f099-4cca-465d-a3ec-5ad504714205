

import React from 'react';

interface CardProps {
  title?: React.ReactNode; // Changed from string to React.ReactNode
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode; // For buttons like "Save", "Add"
}

const Card: React.FC<CardProps> = ({ title, children, className = '', actions }) => {
  return (
    <div className={`bg-white shadow-lg rounded-lg overflow-hidden ${className}`}>
      {title && (
        <div className="px-4 py-3 sm:px-6 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
          {typeof title === 'string' ? (
            <h3 className="text-lg leading-6 font-medium text-gray-900">{title}</h3>
          ) : (
            title // Render as ReactNode if it's not a string
          )}
          {actions && <div>{actions}</div>}
        </div>
      )}
      <div className="px-4 py-5 sm:p-6">
        {children}
      </div>
    </div>
  );
};

export default Card;