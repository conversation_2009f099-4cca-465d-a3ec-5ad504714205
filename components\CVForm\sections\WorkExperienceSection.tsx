
import React, { useState } from 'react';
import { WorkExperienceItem as WorkExperienceType, RepeatableSectionProps } from '../../../types'; // Renamed import
import Input from '../../common/Input';
import Textarea from '../../common/Textarea';
import RepeatableFieldSection, { ItemActions } from '../../common/RepeatableFieldSection';
import RecurringWorkExperienceModal from './RecurringWorkExperienceModal';
import RecurringExperiencePreview from './RecurringExperiencePreview';
import Button from '../../common/Button';
import { v4 as uuidv4 } from 'uuid';
import { useLanguage } from '../../../contexts/LanguageContext';
import { CalendarDays, Repeat } from 'lucide-react';

const createEmptyWorkExperienceItem = (): WorkExperienceType => ({ // Renamed function
  id: uuidv4(),
  startDate: '',
  endDate: '',
  current: false,
  occupationOrPositionHeld: '',
  employer: '',
  employerAddress: '',
  city: '', // Kept for simplicity, or integrate into employerAddress
  country: '', // Kept for simplicity, or integrate into employerAddress
  typeOfBusinessOrSector: '',
  mainActivitiesAndResponsibilities: '',
  isRecurring: false,
  displayAsGroup: false,
});

const WorkExperienceItemComponent: React.FC<{ // Renamed component
  item: WorkExperienceType;
  onChange: (updatedItem: WorkExperienceType) => void;
  onRemove: (id: string) => void;
}> = ({ item, onChange, onRemove }) => {
  const { t } = useLanguage();
  const handleChange = (field: keyof WorkExperienceType, value: any) => {
    onChange({ ...item, [field]: value });
  };

  // Funzione per formattare il periodo ricorrente
  const formatRecurringPeriod = () => {
    if (!item.isRecurring || !item.recurringPattern) return '';
    const { startYear, endYear, startMonth, endMonth } = item.recurringPattern;
    const startMonthName = t(`months.${getMonthKey(startMonth)}`);
    const endMonthName = t(`months.${getMonthKey(endMonth)}`);
    return `${startYear}-${endYear} (${startMonthName}-${endMonthName} ${t('recurringWorkExperience.everyYear')})`;
  };

  const getMonthKey = (month: number): string => {
    const months = ['january', 'february', 'march', 'april', 'may', 'june',
                   'july', 'august', 'september', 'october', 'november', 'december'];
    return months[month - 1];
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative pb-8">
      <ItemActions onRemove={() => onRemove(item.id)} />

      {/* Indicatore per esperienze ricorrenti */}
      {item.isRecurring && (
        <div className="md:col-span-2 mb-2">
          <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-md">
            <Repeat size={16} className="mr-2" />
            <span className="font-medium">{t('recurringWorkExperience.recurringExperience')}</span>
            <span className="ml-2 text-blue-800">{formatRecurringPeriod()}</span>
          </div>
        </div>
      )}

      <Input
        label={t('workExperienceSection.occupationOrPositionHeld')}
        value={item.occupationOrPositionHeld}
        onChange={e => handleChange('occupationOrPositionHeld', e.target.value)}
        required
        containerClassName="md:col-span-2"
      />
      <Input label={t('workExperienceSection.employer')} value={item.employer} onChange={e => handleChange('employer', e.target.value)} required />
      <Input label={t('workExperienceSection.employerAddress')} value={item.employerAddress || ''} onChange={e => handleChange('employerAddress', e.target.value)} />

      {/* Date fields - disabilitati per esperienze ricorrenti */}
      <Input
        label={t('workExperienceSection.startDate')}
        type="month"
        value={item.startDate}
        onChange={e => handleChange('startDate', e.target.value)}
        required
        disabled={item.isRecurring}
        placeholder={item.isRecurring ? t('recurringWorkExperience.managedByPattern') : ''}
      />
      <div>
        <Input
          label={t('workExperienceSection.endDate')}
          type="month"
          value={item.endDate}
          onChange={e => handleChange('endDate', e.target.value)}
          disabled={item.current || item.isRecurring}
          placeholder={item.isRecurring ? t('recurringWorkExperience.managedByPattern') : ''}
        />
        {!item.isRecurring && (
          <label className="flex items-center mt-1">
            <input type="checkbox" checked={item.current} onChange={e => handleChange('current', e.target.checked)} className="mr-2 h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500" />
            {t('workExperienceSection.currentJob')}
          </label>
        )}
      </div>

      <Input label={t('workExperienceSection.typeOfBusinessOrSector')} value={item.typeOfBusinessOrSector || ''} onChange={e => handleChange('typeOfBusinessOrSector', e.target.value)} />

      <div className="md:col-span-2">
        <Textarea
            label={t('workExperienceSection.mainActivitiesAndResponsibilities')}
            value={item.mainActivitiesAndResponsibilities}
            onChange={e => handleChange('mainActivitiesAndResponsibilities', e.target.value)}
            rows={4}
            placeholder="Describe your main tasks and responsibilities."
        />
      </div>

      {/* Anteprima per esperienze ricorrenti */}
      {item.isRecurring && (
        <div className="md:col-span-2">
          <RecurringExperiencePreview workExperience={item} />
        </div>
      )}
    </div>
  );
};

const WorkExperienceSection: React.FC<Omit<RepeatableSectionProps<WorkExperienceType>, 'renderItem' | 'createEmptyItem' | 'title'>> = (props) => {
  const { t } = useLanguage();
  const [isRecurringModalOpen, setIsRecurringModalOpen] = useState(false);

  const handleAddRecurringExperience = (workExperience: WorkExperienceType) => {
    props.setItems([...props.items, workExperience]);
  };

  return (
    <div>
      <RepeatableFieldSection
        {...props}
        title={t('cvForm.workExperienceTitle')}
        addItemText={t('workExperienceSection.addItem')}
        createEmptyItem={createEmptyWorkExperienceItem}
        renderItem={(item, index, onChange, onRemove) => (
          <WorkExperienceItemComponent item={item} onChange={onChange} onRemove={onRemove} />
        )}
      />

      {/* Pulsante per aggiungere esperienza ricorrente */}
      <div className="mt-2">
        <Button
          type="button"
          onClick={() => setIsRecurringModalOpen(true)}
          variant="outline"
          size="sm"
          leftIcon={<CalendarDays size={16} />}
          className="text-blue-600 border-blue-300 hover:bg-blue-50"
        >
          {t('recurringWorkExperience.addRecurringExperience')}
        </Button>
      </div>

      {/* Modal per esperienza ricorrente */}
      <RecurringWorkExperienceModal
        isOpen={isRecurringModalOpen}
        onClose={() => setIsRecurringModalOpen(false)}
        onSave={handleAddRecurringExperience}
      />
    </div>
  );
};
export default WorkExperienceSection;
