
import React from 'react';
import { WorkExperienceItem as WorkExperienceType, RepeatableSectionProps } from '../../../types'; // Renamed import
import Input from '../../common/Input';
import Textarea from '../../common/Textarea';
import RepeatableFieldSection, { ItemActions } from '../../common/RepeatableFieldSection';
import { v4 as uuidv4 } from 'uuid';
import { useLanguage } from '../../../contexts/LanguageContext';

const createEmptyWorkExperienceItem = (): WorkExperienceType => ({ // Renamed function
  id: uuidv4(),
  startDate: '',
  endDate: '',
  current: false,
  occupationOrPositionHeld: '',
  employer: '',
  employerAddress: '',
  city: '', // Kept for simplicity, or integrate into employerAddress
  country: '', // Kept for simplicity, or integrate into employerAddress
  typeOfBusinessOrSector: '',
  mainActivitiesAndResponsibilities: '',
});

const WorkExperienceItemComponent: React.FC<{ // Renamed component
  item: WorkExperienceType;
  onChange: (updatedItem: WorkExperienceType) => void;
  onRemove: (id: string) => void;
}> = ({ item, onChange, onRemove }) => {
  const { t } = useLanguage();
  const handleChange = (field: keyof WorkExperienceType, value: any) => {
    onChange({ ...item, [field]: value });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 relative pb-8">
      <ItemActions onRemove={() => onRemove(item.id)} />
      <Input 
        label={t('workExperienceSection.occupationOrPositionHeld')} 
        value={item.occupationOrPositionHeld} 
        onChange={e => handleChange('occupationOrPositionHeld', e.target.value)} 
        required 
        containerClassName="md:col-span-2"
      />
      <Input label={t('workExperienceSection.employer')} value={item.employer} onChange={e => handleChange('employer', e.target.value)} required />
      <Input label={t('workExperienceSection.employerAddress')} value={item.employerAddress || ''} onChange={e => handleChange('employerAddress', e.target.value)} />
      
      <Input label={t('workExperienceSection.startDate')} type="month" value={item.startDate} onChange={e => handleChange('startDate', e.target.value)} required />
      <div>
        <Input label={t('workExperienceSection.endDate')} type="month" value={item.endDate} onChange={e => handleChange('endDate', e.target.value)} disabled={item.current} />
        <label className="flex items-center mt-1">
          <input type="checkbox" checked={item.current} onChange={e => handleChange('current', e.target.checked)} className="mr-2 h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500" />
          {t('workExperienceSection.currentJob')}
        </label>
      </div>
      <Input label={t('workExperienceSection.typeOfBusinessOrSector')} value={item.typeOfBusinessOrSector || ''} onChange={e => handleChange('typeOfBusinessOrSector', e.target.value)} />
      {/* City and Country can be part of employerAddress or kept separate if simpler for user. */}
      {/* If kept separate:
      <Input label={t('workExperienceSection.city')} value={item.city || ''} onChange={e => handleChange('city', e.target.value)} />
      <Input label={t('workExperienceSection.country')} value={item.country || ''} onChange={e => handleChange('country', e.target.value)} /> 
      */}
      <div className="md:col-span-2">
        <Textarea 
            label={t('workExperienceSection.mainActivitiesAndResponsibilities')} 
            value={item.mainActivitiesAndResponsibilities} 
            onChange={e => handleChange('mainActivitiesAndResponsibilities', e.target.value)} 
            rows={4} 
            placeholder="Describe your main tasks and responsibilities."
        />
      </div>
    </div>
  );
};

const WorkExperienceSection: React.FC<Omit<RepeatableSectionProps<WorkExperienceType>, 'renderItem' | 'createEmptyItem' | 'title'>> = (props) => {
  const { t } = useLanguage();
  return (
    <RepeatableFieldSection
      {...props}
      title={t('cvForm.workExperienceTitle')}
      addItemText={t('workExperienceSection.addItem')}
      createEmptyItem={createEmptyWorkExperienceItem}
      renderItem={(item, index, onChange, onRemove) => (
        <WorkExperienceItemComponent item={item} onChange={onChange} onRemove={onRemove} />
      )}
    />
  );
};
export default WorkExperienceSection;
