import { WorkExperienceItem, RecurringPattern } from '../types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Utility functions for managing recurring work experiences
 */

/**
 * Generates individual work experience items from a recurring pattern
 * Useful for exporting to formats that don't support grouped display
 */
export const expandRecurringExperience = (
  recurringExperience: WorkExperienceItem
): WorkExperienceItem[] => {
  if (!recurringExperience.isRecurring || !recurringExperience.recurringPattern) {
    return [recurringExperience];
  }

  const { recurringPattern } = recurringExperience;
  const { startYear, endYear, startMonth, endMonth } = recurringPattern;
  const expandedExperiences: WorkExperienceItem[] = [];

  for (let year = startYear; year <= endYear; year++) {
    const startDate = `${startMonth.toString().padStart(2, '0')}/${year}`;
    const endDate = `${endMonth.toString().padStart(2, '0')}/${year}`;

    const expandedExperience: WorkExperienceItem = {
      ...recurringExperience,
      id: uuidv4(),
      startDate,
      endDate,
      isRecurring: false,
      displayAsGroup: false,
      recurringPattern: undefined
    };

    expandedExperiences.push(expandedExperience);
  }

  return expandedExperiences;
};

/**
 * Validates a recurring pattern
 */
export const validateRecurringPattern = (pattern: RecurringPattern): string[] => {
  const errors: string[] = [];

  if (pattern.startYear > pattern.endYear) {
    errors.push('Start year must be before or equal to end year');
  }

  if (pattern.startMonth < 1 || pattern.startMonth > 12) {
    errors.push('Start month must be between 1 and 12');
  }

  if (pattern.endMonth < 1 || pattern.endMonth > 12) {
    errors.push('End month must be between 1 and 12');
  }

  if (pattern.startMonth > pattern.endMonth) {
    errors.push('Start month must be before or equal to end month within the same year');
  }

  const currentYear = new Date().getFullYear();
  if (pattern.endYear > currentYear) {
    errors.push('End year cannot be in the future');
  }

  return errors;
};

/**
 * Formats a recurring pattern for display
 */
export const formatRecurringPattern = (
  pattern: RecurringPattern,
  t: (key: string) => string
): string => {
  const getMonthName = (month: number): string => {
    const months = [
      'january', 'february', 'march', 'april', 'may', 'june',
      'july', 'august', 'september', 'october', 'november', 'december'
    ];
    return t(`months.${months[month - 1]}`);
  };

  const startMonthName = getMonthName(pattern.startMonth);
  const endMonthName = getMonthName(pattern.endMonth);
  
  return `${pattern.startYear}-${pattern.endYear} (${startMonthName}-${endMonthName} ${t('recurringWorkExperience.everyYear')})`;
};

/**
 * Calculates the total duration of a recurring experience in months
 */
export const calculateRecurringDuration = (pattern: RecurringPattern): number => {
  const yearsCount = pattern.endYear - pattern.startYear + 1;
  const monthsPerYear = pattern.endMonth - pattern.startMonth + 1;
  return yearsCount * monthsPerYear;
};

/**
 * Checks if two recurring patterns overlap
 */
export const doRecurringPatternsOverlap = (
  pattern1: RecurringPattern,
  pattern2: RecurringPattern
): boolean => {
  // Check if years overlap
  const yearsOverlap = !(pattern1.endYear < pattern2.startYear || pattern2.endYear < pattern1.startYear);
  
  if (!yearsOverlap) {
    return false;
  }

  // Check if months overlap within overlapping years
  const monthsOverlap = !(pattern1.endMonth < pattern2.startMonth || pattern2.endMonth < pattern1.startMonth);
  
  return monthsOverlap;
};

/**
 * Suggests a recurring pattern based on existing work experiences
 */
export const suggestRecurringPattern = (
  experiences: WorkExperienceItem[]
): RecurringPattern | null => {
  // Find experiences with similar job titles and employers
  const groupedExperiences = experiences.reduce((groups, exp) => {
    const key = `${exp.occupationOrPositionHeld}-${exp.employer}`;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(exp);
    return groups;
  }, {} as Record<string, WorkExperienceItem[]>);

  // Look for patterns in the largest group
  let largestGroup: WorkExperienceItem[] = [];
  for (const group of Object.values(groupedExperiences)) {
    if (group.length > largestGroup.length && group.length >= 3) {
      largestGroup = group;
    }
  }

  if (largestGroup.length < 3) {
    return null;
  }

  // Analyze dates to find patterns
  const dates = largestGroup.map(exp => ({
    startMonth: parseInt(exp.startDate.split('/')[0]),
    startYear: parseInt(exp.startDate.split('/')[1]),
    endMonth: parseInt(exp.endDate.split('/')[0]),
    endYear: parseInt(exp.endDate.split('/')[1])
  }));

  // Check if all experiences have the same month range
  const firstDate = dates[0];
  const sameMonthRange = dates.every(date => 
    date.startMonth === firstDate.startMonth && 
    date.endMonth === firstDate.endMonth
  );

  if (!sameMonthRange) {
    return null;
  }

  // Check if years are consecutive or follow a pattern
  const years = dates.map(date => date.startYear).sort((a, b) => a - b);
  const isConsecutive = years.every((year, index) => 
    index === 0 || year === years[index - 1] + 1
  );

  if (!isConsecutive) {
    return null;
  }

  return {
    startYear: Math.min(...years),
    endYear: Math.max(...years),
    startMonth: firstDate.startMonth,
    endMonth: firstDate.endMonth,
    description: 'Suggested pattern based on existing experiences'
  };
};
