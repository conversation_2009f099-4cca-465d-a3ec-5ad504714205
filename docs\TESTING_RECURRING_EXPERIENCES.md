# Test Manuale: Esperienze Lavorative Ricorrenti

## 🎯 Obiettivo del Test
Verificare che la funzionalità delle esperienze ricorrenti funzioni correttamente in tutti gli aspetti:
1. **Caricamento** di esperienze ricorrenti esistenti
2. **Creazione** di nuove esperienze ricorrenti
3. **Modifica** di esperienze ricorrenti esistenti
4. **Salvataggio** e persistenza dei dati
5. **Visualizzazione** nel PDF

## 📋 Checklist di Test

### ✅ Test 1: Caricamento Esperienza Esistente
**Scenario**: Aprire un CV che contiene già un'esperienza ricorrente

**Passi**:
1. Aprire l'applicazione su `http://localhost:5173`
2. Caricare il CV esistente (dovrebbe contenere l'esperienza "Responsabile del Centro Estivo")
3. Andare nella sezione "Esperienza Professionale"

**Risultato Atteso**:
- ✅ L'esperienza dovrebbe essere visualizzata con l'indicatore "Esperienza Ricorrente"
- ✅ Dovrebbe mostrare il badge blu con "2000-2010 (Giugno-Settembre ogni anno)"
- ✅ I campi data dovrebbero essere disabilitati con placeholder "Gestito dallo schema di ricorrenza"
- ✅ Dovrebbe essere presente l'anteprima con statistiche (11 anni, 4 mesi/anno, ecc.)

### ✅ Test 2: Creazione Nuova Esperienza Ricorrente
**Scenario**: Creare una nuova esperienza ricorrente da zero

**Passi**:
1. Cliccare su "Aggiungi Esperienza Ricorrente" (pulsante blu con icona calendario)
2. Compilare il modal con i seguenti dati:
   - **Posizione**: "Istruttore di sci"
   - **Datore di lavoro**: "Scuola Sci Alpina"
   - **Indirizzo**: "Via Monte Bianco 45, Courmayeur"
   - **Settore**: "Sport e Turismo"
   - **Anno inizio**: 2015
   - **Anno fine**: 2020
   - **Mese inizio**: Dicembre (12)
   - **Mese fine**: Marzo (3)
   - **Descrizione pattern**: "Stagione sciistica"
   - **Attività**: "Insegnamento sci alpino a principianti e intermedi\nOrganizzazione lezioni di gruppo\nSicurezza sulle piste"
   - ✅ **Display as group**: Abilitato
3. Cliccare "Salva"

**Risultato Atteso**:
- ✅ L'esperienza dovrebbe apparire nella lista con l'indicatore ricorrente
- ✅ Dovrebbe mostrare "2015-2020 (Dicembre-Marzo ogni anno)"
- ✅ L'anteprima dovrebbe mostrare: 6 anni, 4 mesi/anno, 24 mesi totali

### ✅ Test 3: Modifica Esperienza Ricorrente
**Scenario**: Modificare un'esperienza ricorrente esistente

**Passi**:
1. Trovare l'esperienza ricorrente nella lista
2. Cliccare sul pulsante "Modifica" nel badge blu dell'esperienza ricorrente
3. Verificare che il modal si apra con i dati precompilati
4. Modificare alcuni campi:
   - Cambiare la descrizione del pattern
   - Modificare l'anno di fine (es. da 2010 a 2012)
   - Aggiornare le attività e responsabilità
5. Cliccare "Salva"

**Risultato Atteso**:
- ✅ Il modal dovrebbe aprirsi con il titolo "Modifica Esperienza Lavorativa Ricorrente"
- ✅ Tutti i campi dovrebbero essere precompilati con i dati esistenti
- ✅ Le modifiche dovrebbero essere applicate all'esperienza esistente (non creare una nuova)
- ✅ L'anteprima dovrebbe aggiornarsi con i nuovi dati
- ✅ Il badge dovrebbe mostrare il nuovo periodo aggiornato

### ✅ Test 4: Interfaccia di Modifica
**Scenario**: Verificare che l'interfaccia di modifica sia intuitiva e funzionale

**Passi**:
1. Identificare un'esperienza ricorrente nella lista
2. Verificare la presenza del pulsante "Modifica" nel badge blu
3. Cliccare sul pulsante "Modifica"
4. Verificare che il modal si apra correttamente
5. Controllare che tutti i campi siano precompilati
6. Modificare alcuni valori e salvare
7. Riaprire il modal di modifica per verificare la persistenza

**Risultato Atteso**:
- ✅ Il pulsante "Modifica" dovrebbe essere visibile e accessibile
- ✅ Il modal dovrebbe aprirsi con il titolo corretto
- ✅ Tutti i campi dovrebbero contenere i valori attuali
- ✅ Le modifiche dovrebbero essere salvate correttamente
- ✅ Riaprendo il modal, i nuovi valori dovrebbero essere presenti

### ✅ Test 5: Visualizzazione PDF
**Scenario**: Verificare come l'esperienza ricorrente appare nel PDF

**Passi**:
1. Andare nella sezione "Anteprima PDF"
2. Verificare la sezione "Esperienza Professionale"

**Risultato Atteso**:
- ✅ L'esperienza dovrebbe apparire come "2000-2010 (Giugno-Settembre ogni anno)"
- ✅ Se presente, la descrizione del pattern dovrebbe essere visibile in blu
- ✅ Il formato dovrebbe essere pulito e professionale

### ✅ Test 6: Salvataggio e Persistenza
**Scenario**: Verificare che i dati vengano salvati correttamente

**Passi**:
1. Creare o modificare un'esperienza ricorrente
2. Salvare il CV
3. Ricaricare la pagina
4. Verificare che l'esperienza sia ancora presente e corretta

**Risultato Atteso**:
- ✅ L'esperienza dovrebbe essere caricata correttamente
- ✅ Tutti i campi ricorrenti dovrebbero essere preservati
- ✅ L'anteprima dovrebbe funzionare come prima

### ✅ Test 7: Validazione Errori
**Scenario**: Testare la validazione dei dati

**Passi**:
1. Aprire il modal per esperienza ricorrente
2. Inserire dati non validi:
   - Anno fine prima di anno inizio
   - Mesi non validi
   - Campi obbligatori vuoti
3. Tentare di salvare

**Risultato Atteso**:
- ✅ Il sistema dovrebbe impedire il salvataggio
- ✅ Dovrebbero apparire messaggi di errore appropriati
- ✅ I campi con errori dovrebbero essere evidenziati

## 🐛 Problemi Noti e Soluzioni

### Problema: Esperienza non appare come ricorrente dopo il caricamento
**Causa**: I campi `isRecurring`, `recurringPattern`, `displayAsGroup` non sono presenti nel JSON
**Soluzione**: Verificare che il file `cvs.json` contenga tutti i campi necessari

### Problema: Date nel formato sbagliato
**Causa**: Conversione tra formato `MM/YYYY` e `YYYY-MM`
**Soluzione**: Assicurarsi che il modal generi date nel formato `YYYY-MM`

### Problema: Anteprima non si aggiorna
**Causa**: Stato del componente non sincronizzato
**Soluzione**: Verificare che `onChange` sia chiamato correttamente

## 📊 Dati di Test

### Esperienza Ricorrente di Test 1
```json
{
  "id": "test-recurring-1",
  "startDate": "2000-06",
  "endDate": "2010-09",
  "current": false,
  "occupationOrPositionHeld": "Responsabile del Centro Estivo",
  "employer": "Tennis Club Vicopelago, Lucca",
  "employerAddress": "",
  "typeOfBusinessOrSector": "Centro Estivo",
  "mainActivitiesAndResponsibilities": "Gestione del centro estivo, coordinamento delle attività e supervisione del personale",
  "isRecurring": true,
  "displayAsGroup": true,
  "recurringPattern": {
    "startYear": 2000,
    "endYear": 2010,
    "startMonth": 6,
    "endMonth": 9,
    "description": "Lavoro estivo stagionale"
  }
}
```

### Esperienza Ricorrente di Test 2
```json
{
  "id": "test-recurring-2",
  "startDate": "2015-12",
  "endDate": "2020-03",
  "current": false,
  "occupationOrPositionHeld": "Istruttore di sci",
  "employer": "Scuola Sci Alpina",
  "employerAddress": "Via Monte Bianco 45, Courmayeur",
  "typeOfBusinessOrSector": "Sport e Turismo",
  "mainActivitiesAndResponsibilities": "Insegnamento sci alpino a principianti e intermedi\nOrganizzazione lezioni di gruppo\nSicurezza sulle piste",
  "isRecurring": true,
  "displayAsGroup": true,
  "recurringPattern": {
    "startYear": 2015,
    "endYear": 2020,
    "startMonth": 12,
    "endMonth": 3,
    "description": "Stagione sciistica"
  }
}
```

## ✅ Risultati Attesi

Al completamento di tutti i test, dovresti avere:

1. **Funzionalità Completa**: Tutte le operazioni CRUD per esperienze ricorrenti
2. **Persistenza Dati**: Salvataggio e caricamento corretto dal JSON
3. **UI Intuitiva**: Interfaccia chiara con feedback visivo appropriato
4. **PDF Professionale**: Visualizzazione pulita e leggibile nel documento finale
5. **Validazione Robusta**: Prevenzione di errori e dati inconsistenti

## 🚀 Prossimi Passi

Se tutti i test passano:
- ✅ La funzionalità è pronta per l'uso in produzione
- ✅ Documentare eventuali casi d'uso aggiuntivi
- ✅ Considerare miglioramenti futuri (pattern più complessi, template predefiniti, ecc.)

Se alcuni test falliscono:
- 🔧 Identificare e correggere i problemi
- 🔧 Ripetere i test fino al successo completo
- 🔧 Aggiornare la documentazione se necessario
