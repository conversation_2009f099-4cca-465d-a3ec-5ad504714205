{"name": "cv-pro-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/index.js", "dev:full": "concurrently \"npm run dev\" \"npm run server\""}, "dependencies": {"lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "sql.js": "^1.13.0", "uuid": "^11.1.0", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}