import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  containerClassName?: string;
}

const Input: React.FC<InputProps> = ({ label, id, containerClassName = 'mb-4', ...props }) => {
  return (
    <div className={containerClassName}>
      {label && <label htmlFor={id || props.name} className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <input
        id={id || props.name}
        className="mt-1 block w-full px-3 py-2 bg-white text-gray-900 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
        {...props}
      />
    </div>
  );
};

export default Input;