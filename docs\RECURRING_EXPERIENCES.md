# Esperienze Lavorative Ricorrenti

## Panoramica

La funzionalità delle **Esperienze Lavorative Ricorrenti** risolve il problema comune di dover inserire manualmente la stessa esperienza lavorativa per più anni consecutivi (ad esempio, lavori stagionali o estivi).

## Problema Risolto

**Prima**: Per un lavoro estivo dal 2000 al 2010 (giugno-settembre ogni anno), era necessario:
- Creare 11 voci separate
- Inserire manualmente le date per ogni anno
- Ripetere le stesse informazioni 11 volte

**Ora**: È possibile:
- Creare una singola esperienza ricorrente
- Definire il pattern temporale una volta sola
- Scegliere come visualizzarla nel PDF (compatta o espansa)

## Come Utilizzare

### 1. Aggiungere un'Esperienza Ricorrente

1. Nella sezione "Esperienza Professionale", cliccare su **"Aggiungi Esperienza Ricorrente"**
2. Compilare i dati base del lavoro:
   - Posizione lavorativa
   - Datore di lavoro
   - Indirizzo
   - Tipo di attività
   - Mansioni e responsabilità

### 2. Configurare il Pattern di Ricorrenza

3. Definire il pattern temporale:
   - **Anno di inizio**: es. 2000
   - **Anno di fine**: es. 2010
   - **Mese di inizio**: es. Giugno (6)
   - **Mese di fine**: es. Settembre (9)
   - **Descrizione pattern**: es. "Lavoro estivo"

### 3. Scegliere la Modalità di Visualizzazione

4. Decidere come mostrare l'esperienza nel PDF:
   - **Compatta**: Una singola voce "2000-2010 (Giugno-Settembre ogni anno)"
   - **Espansa**: Voci separate per ogni anno (2000, 2001, 2002, ...)

## Funzionalità Avanzate

### Anteprima Intelligente

Il sistema mostra automaticamente:
- **Statistiche**: Anni totali, mesi per anno, periodi totali
- **Anteprima del formato**: Come apparirà nel PDF
- **Vista espansa**: Elenco di tutti i periodi generati

### Validazione Automatica

Il sistema verifica:
- Che l'anno di inizio sia precedente a quello di fine
- Che i mesi siano validi (1-12)
- Che l'anno di fine non sia nel futuro
- Che il mese di inizio sia precedente a quello di fine

### Utilità Integrate

- **Calcolo durata**: Calcolo automatico dei mesi totali di esperienza
- **Rilevamento sovrapposizioni**: Controllo di conflitti con altre esperienze
- **Suggerimenti automatici**: Analisi delle esperienze esistenti per suggerire pattern

## Esempi di Utilizzo

### Esempio 1: Lavoro Estivo
```
Posizione: Animatore turistico
Datore di lavoro: Resort Mediterraneo
Pattern: 2018-2022, Giugno-Settembre
Risultato PDF: "2018-2022 (Giugno-Settembre ogni anno)"
```

### Esempio 2: Insegnamento Stagionale
```
Posizione: Istruttore di sci
Datore di lavoro: Scuola Sci Alpina
Pattern: 2015-2020, Dicembre-Marzo
Risultato PDF: "2015-2020 (Dicembre-Marzo ogni anno)"
```

### Esempio 3: Consulenza Periodica
```
Posizione: Consulente fiscale
Datore di lavoro: Studio Commercialisti Associati
Pattern: 2019-2023, Gennaio-Aprile
Risultato PDF: "2019-2023 (Gennaio-Aprile ogni anno)"
```

## Vantaggi

### Per l'Utente
- **Risparmio di tempo**: Inserimento una volta sola invece di N volte
- **Riduzione errori**: Meno possibilità di errori di battitura o date
- **Flessibilità**: Scelta tra visualizzazione compatta o dettagliata
- **Chiarezza**: Anteprima immediata del risultato finale

### Per il CV
- **Professionalità**: Formato pulito e organizzato
- **Leggibilità**: Informazioni chiare e ben strutturate
- **Completezza**: Tutte le informazioni necessarie senza ridondanza
- **Personalizzazione**: Adattabile alle preferenze del candidato

## Implementazione Tecnica

### Nuovi Tipi TypeScript
```typescript
interface RecurringPattern {
  startYear: number;
  endYear: number;
  startMonth: number;
  endMonth: number;
  description?: string;
}

interface WorkExperienceItem {
  // ... campi esistenti
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
  displayAsGroup?: boolean;
}
```

### Componenti Aggiunti
- `RecurringWorkExperienceModal`: Modal per configurare esperienze ricorrenti
- `RecurringExperiencePreview`: Anteprima con statistiche e dettagli
- `recurringExperienceUtils`: Utilità per gestione e validazione

### Funzionalità di Supporto
- Espansione automatica per formati che non supportano raggruppamento
- Validazione pattern con messaggi di errore localizzati
- Calcolo statistiche (durata totale, periodi, ecc.)
- Rilevamento sovrapposizioni tra esperienze

## Compatibilità

- ✅ **PDF Export**: Supporto completo per entrambe le modalità
- ✅ **Multilingua**: Traduzioni complete in inglese e italiano
- ✅ **Responsive**: Interfaccia ottimizzata per desktop e mobile
- ✅ **Accessibilità**: Componenti accessibili con screen reader

## Roadmap Future

### Possibili Miglioramenti
- **Pattern più complessi**: Supporto per pattern non consecutivi
- **Importazione automatica**: Rilevamento automatico di pattern da CV esistenti
- **Template predefiniti**: Pattern comuni preconfigurati
- **Esportazione avanzata**: Formati aggiuntivi con supporto nativo per ricorrenze

### Feedback e Contributi
Per suggerimenti o segnalazioni di bug relativi alle esperienze ricorrenti, aprire una issue nel repository del progetto.
