
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { LanguageCode, Translations, LanguageContextType } from '../types';

const translations: Record<LanguageCode, Translations> = {
  en: {
    headerTitle: "CV Pro Manager",
    footerMessage: "© {year} CV Pro Manager. All rights reserved.",
    languageSelector: "Language:",
    languages: {
      en: "English",
      it: "Italiano"
    },
    dashboard: {
      title: "My CVs",
      createNewCV: "Create New CV",
      noCVsFound: "No CVs Found",
      getStarted: "Get started by creating your first CV.",
      lastUpdated: "Last updated: {date}",
      candidate: "Candidate: {name}", // Will use name + surname
      actions: {
        edit: "Edit",
        preview: "Preview",
        duplicate: "Duplicate",
        delete: "Delete"
      },
      confirmDelete: "Are you sure you want to delete CV \"{cvName}\"? This action cannot be undone.",
      deleteSuccess: "CV \"{cvName}\" deleted.",
      deleteError: "Failed to delete CV.",
      duplicateSuccess: "CV \"{cvName}\" duplicated successfully!",
      duplicateError: "Failed to duplicate CV."
    },
    cvEditor: {
      createTitle: "Create New CV",
      editTitle: "Edit CV: {cvName}",
      backToDashboard: "Back to Dashboard",
      loading: "Loading CV data...",
      notFound: "CV not found. Redirecting to dashboard.",
      errorNotFound: "Error: CV not found.",
      saveSuccess: "CV \"{cvName}\" {action} successfully!",
      actionCreated: "created",
      actionUpdated: "updated",
      previewUnsavedError: "Please save the CV first to enable preview."
    },
    pdfPreview: {
      loading: "Loading preview...",
      notFound: "CV Data Not Available",
      notFoundMessage: "Could not load CV data for preview.",
      backToDashboard: "Back to Dashboard",
      backToEditor: "Back to Editor",
      title: "Preview: {cvName}",
      downloadPDF: "Download PDF",
      downloadSuccess: "PDF downloaded successfully!",
      downloadError: "Failed to generate PDF.",
      tempDataError: "Temporary preview data not found.",
      tempDataMismatch: "Preview data mismatch."
    },
    // EUROPASS Form Section Titles & Fields
    cvForm: {
      cvTitleLabel: "CV Title", // Changed from cvNameLabel
      cvTitlePlaceholder: "e.g., CV John Doe - Marketing Specialist",
      // Section titles matching Europass structure
      personalInformationTitle: "PERSONAL INFORMATION",
      jobAppliedForTitle: "JOB APPLIED FOR / PREFERRED JOB / STUDIES APPLIED FOR / PERSONAL STATEMENT", // Europass specific
      workExperienceTitle: "WORK EXPERIENCE",
      educationTrainingTitle: "EDUCATION AND TRAINING",
      personalSkillsTitle: "PERSONAL SKILLS",
      // Optional Europass sections (can be grouped under Additional Information or specific)
      drivingLicenceTitle: "DRIVING LICENCE",
      additionalInformationTitle: "ADDITIONAL INFORMATION", // (e.g. Hobbies, Projects, Publications, References)
      attachmentsTitle: "ATTACHMENTS",
      buttons: {
        previewPDF: "Preview PDF",
        saveCV: "Save CV"
      }
    },
    personalInfoSection: {
      name: "Name", // Changed from firstName
      surname: "Surname", // Changed from lastName
      photo: "Photo",
      photoUpload: "Upload Photo",
      photoChange: "Change Photo",
      photoRemove: "Remove Photo",
      address: "Address (Number, street, postcode, city, country)",
      postalCode: "Postal Code",
      city: "City",
      country: "Country",
      telephone: "Telephone(s)",
      addTelephone: "Add telephone",
      placeholderTelephone: "Enter telephone number",
      email: "Email(s)",
      addEmail: "Add email",
      placeholderEmail: "Enter email address",
      website: "Website(s) / Portfolio / Social Media",
      addWebsite: "Add website",
      placeholderWebsite: "Enter URL",
      linkedinProfile: "LinkedIn Profile URL",
      instantMessaging: "Instant Messaging",
      addIM: "Add Instant Messaging",
      imService: "Service (e.g. Skype)",
      imUsername: "Username/Address",
      sex: "Sex",
      sexOptions: {
        select: "Select Sex (Optional)",
        male: "Male",
        female: "Female",
        unspecified: "Unspecified" // Europass often uses this
      },
      dateOfBirth: "Date of Birth (dd/mm/yyyy)",
      nationality: "Nationality"
    },
    jobAppliedForSection: { // Corresponds to desiredOccupation/professionalSector
      label: "Specify job applied for, preferred job, studies applied for, or write a personal statement"
    },
    workExperienceSection: {
      addItem: "Add Work Experience",
      occupationOrPositionHeld: "Occupation or Position Held",
      employer: "Employer",
      employerAddress: "Employer Address (Street, City, Country)",
      typeOfBusinessOrSector: "Type of Business or Sector",
      mainActivitiesAndResponsibilities: "Main Activities and Responsibilities",
      startDate: "Start Date (mm/yyyy)",
      endDate: "End Date (mm/yyyy)",
      currentJob: "Currently working here"
    },
    educationSection: {
      addItem: "Add Education/Training",
      titleOfQualificationAwarded: "Title of Qualification Awarded",
      organisationName: "Name of Organisation",
      organisationAddress: "Address of Organisation (Street, City, Country)",
      mainSubjectsOrSkillsCovered: "Main Subjects / Occupational Skills Covered",
      levelInClassification: "Level in National or International Classification (if applicable)",
      startDate: "Start Date (mm/yyyy)",
      endDate: "End Date (mm/yyyy)",
      currentlyStudying: "Currently studying here"
    },
    personalSkillsSection: {
      motherTongue: "Mother Tongue",
      placeholderMotherTongue: "Specify mother tongue",
      otherLanguages: "Other Language(s)",
      addLanguage: "Add Language",
      language: "Language",
      // CEFR Levels
      understanding: "Understanding",
      listening: "Listening",
      reading: "Reading",
      speaking: "Speaking",
      spokenInteraction: "Spoken Interaction",
      spokenProduction: "Spoken Production",
      writing: "Writing", // Single 'Writing' category in Europass
      // Other skills
      communicationSkills: "Communication Skills",
      organisationalOrManagerialSkills: "Organisational / Managerial Skills",
      professionalSkills: "Professional Skills (Job-related skills)", // Or "Technical Skills"
      digitalSkills: "Digital Skills",
      artisticSkills: "Artistic Skills",
      otherSkills: "Other Skills"
    },
    drivingLicenceSection: {
      label: "Categories (e.g., B, C1)"
    },
    additionalInfoSection: {
      label: "Include any other relevant information (e.g., publications, projects, conferences, seminars, honours and awards, memberships, hobbies, references)"
    },
    attachmentsSection: {
      title: "Attachments",
      addItem: "Add Attachment",
      name: "Attachment Name / Title",
      description: "Brief Description (Optional)"
    },
    skillLevels: { // Based on CEFR
      A1: "A1",
      A2: "A2",
      B1: "B1",
      B2: "B2",
      C1: "C1",
      C2: "C2",
      NATIVE: "Native" // Simplified
    },
    placeholders: {
      selectLevel: "Select Level",
      select: "Select...",
      enterHere: "Enter here..."
    },
    // PDF specific translations, often section titles are UPPERCASE
    pdf: {
      personalInformationTitle: "PERSONAL INFORMATION",
      jobAppliedForTitle: "JOB APPLIED FOR / PREFERRED JOB / STUDIES APPLIED FOR / PERSONAL STATEMENT",
      workExperienceTitle: "WORK EXPERIENCE",
      educationTrainingTitle: "EDUCATION AND TRAINING",
      personalSkillsTitle: "PERSONAL SKILLS",
      drivingLicenceTitle: "DRIVING LICENCE",
      additionalInformationTitle: "ADDITIONAL INFORMATION",
      attachmentsTitle: "ATTACHMENTS",
      // Field labels for PDF (can be same as form or shorter)
      name: "Name",
      surname: "Surname",
      address: "Address",
      telephone: "Telephone(s)",
      email: "Email(s)",
      website: "Website(s)",
      linkedin: "LinkedIn",
      instantMessaging: "Instant Messaging",
      sex: "Sex",
      dateOfBirth: "Date of birth",
      nationality: "Nationality",
      photo: "Photo", // if displaying a label for it
      
      occupationOrPositionHeld: "Occupation or position held",
      employer: "Employer",
      employerAddress: "Employer address",
      typeOfBusinessOrSector: "Type of business or sector",
      mainActivitiesAndResponsibilities: "Main activities and responsibilities",
      dates: "Dates", // For date ranges
      present: "Present",

      titleOfQualificationAwarded: "Title of qualification awarded",
      organisationNameAndAddress: "Name and address of organisation",
      organisationName: "Name of organisation",
      mainSubjectsOrSkillsCovered: "Main subjects/Skills covered",
      levelInClassification: "Level in classification",

      motherTongue: "Mother tongue(s)", // Even if data is singular, title can be plural
      otherLanguages: "Other language(s)",
      languageTable: { // For CEFR grid
        language: "Language",
        understanding: "UNDERSTANDING",
        speaking: "SPEAKING",
        writing: "WRITING", // Europass specific column header
        listening: "Listening",
        reading: "Reading",
        spokenInteraction: "Spoken interaction",
        spokenProduction: "Spoken production",
      },
      // Other skills titles for PDF
      communicationSkills: "Communication skills",
      organisationalOrManagerialSkills: "Organisational / managerial skills",
      professionalSkills: "Professional skills",
      digitalSkills: "Digital skills",
      artisticSkills: "Artistic skills",
      otherSkills: "Other skills",

      drivingCategories: "Categories",
      attachmentName: "Name",
      attachmentDescription: "Description",

      // Generic
      page: "Page {currentPage} of {totalPages}"
    },
    errors: {
      loadingFailed: "Failed to load CVs from server",
      saveFailed: "Failed to save CV to server",
      updateFailed: "Failed to update CV on server",
      deleteFailed: "Failed to delete CV from server",
    }
  },
  it: { // Italian translations mirroring the English structure
    headerTitle: "Gestore CV Pro",
    footerMessage: "© {year} Gestore CV Pro. Tutti i diritti riservati.",
    languageSelector: "Lingua:",
    languages: {
      en: "Inglese",
      it: "Italiano"
    },
    dashboard: {
      title: "I Miei CV",
      createNewCV: "Crea Nuovo CV",
      noCVsFound: "Nessun CV Trovato",
      getStarted: "Inizia creando il tuo primo CV.",
      lastUpdated: "Ultimo aggiornamento: {date}",
      candidate: "Candidato: {name}",
      actions: {
        edit: "Modifica",
        preview: "Anteprima",
        duplicate: "Duplica",
        delete: "Elimina"
      },
      confirmDelete: "Sei sicuro di voler eliminare il CV \"{cvName}\"? Questa azione non può essere annullata.",
      deleteSuccess: "CV \"{cvName}\" eliminato.",
      deleteError: "Errore durante l'eliminazione del CV.",
      duplicateSuccess: "CV \"{cvName}\" duplicato con successo!",
      duplicateError: "Errore durante la duplicazione del CV."
    },
    cvEditor: {
      createTitle: "Crea Nuovo CV",
      editTitle: "Modifica CV: {cvName}",
      backToDashboard: "Torna alla Dashboard",
      loading: "Caricamento dati CV...",
      notFound: "CV non trovato. Reindirizzamento alla dashboard.",
      errorNotFound: "Errore: CV non trovato.",
      saveSuccess: "CV \"{cvName}\" {action} con successo!",
      actionCreated: "creato",
      actionUpdated: "aggiornato",
      previewUnsavedError: "Salva prima il CV per abilitare l'anteprima."
    },
    pdfPreview: {
      loading: "Caricamento anteprima...",
      notFound: "Dati CV Non Disponibili",
      notFoundMessage: "Impossibile caricare i dati del CV per l'anteprima.",
      backToDashboard: "Torna alla Dashboard",
      backToEditor: "Torna all'Editor",
      title: "Anteprima: {cvName}",
      downloadPDF: "Scarica PDF",
      downloadSuccess: "PDF scaricato con successo!",
      downloadError: "Errore durante la generazione del PDF.",
      tempDataError: "Dati di anteprima temporanei non trovati.",
      tempDataMismatch: "Mancata corrispondenza dei dati di anteprima."
    },
    cvForm: {
      cvTitleLabel: "Titolo CV",
      cvTitlePlaceholder: "Es., CV Mario Rossi - Specialista Marketing",
      personalInformationTitle: "INFORMAZIONI PERSONALI",
      jobAppliedForTitle: "POSIZIONE LAVORATIVA DESIDERATA / POSIZIONE PREFERITA / STUDI PER CUI CI SI CANDIDA / PROFILO PERSONALE",
      workExperienceTitle: "ESPERIENZA PROFESSIONALE",
      educationTrainingTitle: "ISTRUZIONE E FORMAZIONE",
      personalSkillsTitle: "COMPETENZE PERSONALI",
      drivingLicenceTitle: "PATENTE DI GUIDA",
      additionalInformationTitle: "INFORMAZIONI SUPPLEMENTARI",
      attachmentsTitle: "ALLEGATI",
      buttons: {
        previewPDF: "Anteprima PDF",
        saveCV: "Salva CV"
      }
    },
    personalInfoSection: {
      name: "Nome",
      surname: "Cognome",
      photo: "Foto",
      photoUpload: "Carica Foto",
      photoChange: "Cambia Foto",
      photoRemove: "Rimuovi Foto",
      address: "Indirizzo (Numero, via, CAP, città, paese)",
      postalCode: "CAP",
      city: "Città",
      country: "Paese",
      telephone: "Telefono(i)",
      addTelephone: "Aggiungi telefono",
      placeholderTelephone: "Inserisci numero di telefono",
      email: "Email",
      addEmail: "Aggiungi email",
      placeholderEmail: "Inserisci indirizzo email",
      website: "Sito(i) web / Portfolio / Social Media",
      addWebsite: "Aggiungi sito web",
      placeholderWebsite: "Inserisci URL",
      linkedinProfile: "Profilo LinkedIn (URL)",
      instantMessaging: "Messaggistica Istantanea",
      addIM: "Aggiungi Messaggistica Istantanea",
      imService: "Servizio (es. Skype)",
      imUsername: "Nome utente/Indirizzo",
      sex: "Sesso",
      sexOptions: {
        select: "Seleziona Sesso (Opzionale)",
        male: "Maschio",
        female: "Femmina",
        unspecified: "Non specificato"
      },
      dateOfBirth: "Data di Nascita (gg/mm/aaaa)",
      nationality: "Nazionalità"
    },
    jobAppliedForSection: {
      label: "Specificare la posizione lavorativa desiderata, la posizione preferita, gli studi per cui ci si candida o scrivere un profilo personale"
    },
    workExperienceSection: {
      addItem: "Aggiungi Esperienza Professionale",
      occupationOrPositionHeld: "Occupazione o Posizione Ricoperta",
      employer: "Datore di Lavoro",
      employerAddress: "Indirizzo Datore di Lavoro (Via, Città, Paese)",
      typeOfBusinessOrSector: "Tipo di Attività o Settore",
      mainActivitiesAndResponsibilities: "Principali Mansioni e Responsabilità",
      startDate: "Data Inizio (mm/aaaa)",
      endDate: "Data Fine (mm/aaaa)",
      currentJob: "Attualmente impiegato qui"
    },
    educationSection: {
      addItem: "Aggiungi Istruzione/Formazione",
      titleOfQualificationAwarded: "Titolo della Qualifica Rilasciata",
      organisationName: "Nome dell'Organizzazione",
      organisationAddress: "Indirizzo dell'Organizzazione (Via, Città, Paese)",
      mainSubjectsOrSkillsCovered: "Principali Materie / Competenze Professionali Acquisite",
      levelInClassification: "Livello nella Classificazione Nazionale o Internazionale (se pertinente)",
      startDate: "Data Inizio (mm/aaaa)",
      endDate: "Data Fine (mm/aaaa)",
      currentlyStudying: "Attualmente in corso di studi"
    },
    personalSkillsSection: {
      motherTongue: "Lingua Madre",
      placeholderMotherTongue: "Specificare lingua madre",
      otherLanguages: "Altra(e) Lingua(e)",
      addLanguage: "Aggiungi Lingua",
      language: "Lingua",
      understanding: "Comprensione",
      listening: "Ascolto",
      reading: "Lettura",
      speaking: "Parlato",
      spokenInteraction: "Interazione Orale",
      spokenProduction: "Produzione Orale",
      writing: "Produzione Scritta",
      communicationSkills: "Competenze Comunicative",
      organisationalOrManagerialSkills: "Competenze Organizzative / Gestionali",
      professionalSkills: "Competenze Professionali (relative al lavoro)",
      digitalSkills: "Competenze Digitali",
      artisticSkills: "Competenze Artistiche",
      otherSkills: "Altre Competenze"
    },
    drivingLicenceSection: {
      label: "Categorie (es. B, C1)"
    },
    additionalInfoSection: {
      label: "Includere qualsiasi altra informazione pertinente (es. pubblicazioni, progetti, conferenze, seminari, riconoscimenti e premi, appartenenze, hobby, referenze)"
    },
    attachmentsSection: {
      title: "Allegati",
      addItem: "Aggiungi Allegato",
      name: "Nome / Titolo Allegato",
      description: "Breve Descrizione (Opzionale)"
    },
    skillLevels: {
      A1: "A1",
      A2: "A2",
      B1: "B1",
      B2: "B2",
      C1: "C1",
      C2: "C2",
      NATIVE: "Madrelingua"
    },
    placeholders: {
      selectLevel: "Seleziona Livello",
      select: "Seleziona...",
      enterHere: "Inserisci qui..."
    },
    pdf: {
      personalInformationTitle: "INFORMAZIONI PERSONALI",
      jobAppliedForTitle: "POSIZIONE LAVORATIVA DESIDERATA / POSIZIONE PREFERITA / STUDI PER CUI CI SI CANDIDA / PROFILO PERSONALE",
      workExperienceTitle: "ESPERIENZA PROFESSIONALE",
      educationTrainingTitle: "ISTRUZIONE E FORMAZIONE",
      personalSkillsTitle: "COMPETENZE PERSONALI",
      drivingLicenceTitle: "PATENTE DI GUIDA",
      additionalInformationTitle: "INFORMAZIONI SUPPLEMENTARI",
      attachmentsTitle: "ALLEGATI",
      name: "Nome",
      surname: "Cognome",
      address: "Indirizzo",
      telephone: "Telefono(i)",
      email: "Email",
      website: "Sito(i) web",
      linkedin: "LinkedIn",
      instantMessaging: "Messaggistica Istantanea",
      sex: "Sesso",
      dateOfBirth: "Data di nascita",
      nationality: "Nazionalità",
      photo: "Foto",
      occupationOrPositionHeld: "Occupazione o posizione ricoperta",
      employer: "Datore di lavoro",
      employerAddress: "Indirizzo datore di lavoro",
      typeOfBusinessOrSector: "Tipo di attività o settore",
      mainActivitiesAndResponsibilities: "Principali mansioni e responsabilità",
      dates: "Date",
      present: "Presente",
      titleOfQualificationAwarded: "Titolo della qualifica rilasciata",
      organisationNameAndAddress: "Nome e indirizzo dell'organizzazione",
      organisationName: "Nome dell'organizzazione",
      mainSubjectsOrSkillsCovered: "Principali materie/Competenze professionali acquisite",
      levelInClassification: "Livello nella classificazione",
      motherTongue: "Lingua(e) madre",
      otherLanguages: "Altra(e) lingua(e)",
      languageTable: {
        language: "Lingua",
        understanding: "COMPRENSIONE",
        speaking: "PARLATO",
        writing: "PRODUZIONE SCRITTA",
        listening: "Ascolto",
        reading: "Lettura",
        spokenInteraction: "Interazione orale",
        spokenProduction: "Produzione orale",
      },
      communicationSkills: "Competenze comunicative",
      organisationalOrManagerialSkills: "Competenze organizzative / gestionali",
      professionalSkills: "Competenze professionali",
      digitalSkills: "Competenze digitali",
      artisticSkills: "Competenze artistiche",
      otherSkills: "Altre competenze",
      drivingCategories: "Categorie",
      attachmentName: "Nome",
      attachmentDescription: "Descrizione",
      page: "Pagina {currentPage} di {totalPages}"
    },
    errors: {
      loadingFailed: "Impossibile caricare i CV dal server",
      saveFailed: "Impossibile salvare il CV sul server",
      updateFailed: "Impossibile aggiornare il CV sul server",
      deleteFailed: "Impossibile eliminare il CV dal server",
    }
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<LanguageCode>(() => {
    const storedLang = localStorage.getItem('cvProManagerLanguage') as LanguageCode;
    return storedLang && translations[storedLang] ? storedLang : 'en';
  });

  useEffect(() => {
    localStorage.setItem('cvProManagerLanguage', language);
    document.documentElement.lang = language; // Set lang attribute on HTML element
  }, [language]);

  const setLanguage = (lang: LanguageCode) => {
    if (translations[lang]) {
      setLanguageState(lang);
    }
  };

  const t = useCallback((key: string, replacements?: Record<string, string | number>): string => {
    const keys = key.split('.');
    let result: string | Translations | undefined = translations[language];
    for (const k of keys) {
      if (typeof result === 'object' && result !== null && k in result) {
        result = (result as Translations)[k];
      } else {
        result = undefined;
        break;
      }
    }

    if (typeof result === 'string') {
      if (replacements) {
        return Object.entries(replacements).reduce((acc, [placeholder, value]) => {
          return acc.replace(new RegExp(`{${placeholder}}`, 'g'), String(value));
        }, result);
      }
      return result;
    }
    
    if (language !== 'en') {
      let fallbackResult: string | Translations | undefined = translations.en;
      for (const k of keys) {
        if (typeof fallbackResult === 'object' && fallbackResult !== null && k in fallbackResult) {
          fallbackResult = (fallbackResult as Translations)[k];
        } else {
          fallbackResult = undefined;
          break;
        }
      }
      if (typeof fallbackResult === 'string') {
         if (replacements) {
          return Object.entries(replacements).reduce((acc, [placeholder, value]) => {
            return acc.replace(new RegExp(`{${placeholder}}`, 'g'), String(value));
          }, fallbackResult);
        }
        return fallbackResult;
      }
    }
    console.warn(`Translation key not found: ${key} for language ${language}`);
    return key; 
  }, [language]);

  const getLocalizedDate = useCallback((dateString: string, options?: Intl.DateTimeFormatOptions): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const locale = language === 'it' ? 'it-IT' : 'en-GB';
    
    const defaultOptions: Intl.DateTimeFormatOptions = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };

    if (dateString.match(/^\d{4}-\d{2}$/)) { // YYYY-MM format
      defaultOptions.month = 'short';
      delete defaultOptions.day;
    }


    return date.toLocaleDateString(locale, { ...defaultOptions, ...options});
  }, [language]);


  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, getLocalizedDate }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
