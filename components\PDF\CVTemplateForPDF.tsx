
import React from 'react';
import { CVData, WorkExperienceItem, EducationTrainingItem, LanguageSkill, Attachment } from '../../types'; // SkillLevelKey not directly used here, but implied by t(`skillLevels...`)
import { useLanguage } from '../../contexts/LanguageContext'; // For standalone use, pass t and getLocalizedDate


interface CVTemplateForPDFProps {
  cvData: CVData;
  t: (key: string, replacements?: Record<string, string | number>) => string; // Make t prop explicit
  getLocalizedDate: (dateString: string, options?: Intl.DateTimeFormatOptions) => string; // Make getLocalizedDate prop explicit
}

const Section: React.FC<{ title: string; children: React.ReactNode; className?: string }> = ({ title, children, className }) => (
  <div className={`flex mt-3 ${className || ''}`}>
    <div className="w-1/4 pr-4"> {/* Left column for section title */}
      <h2 className="text-xs font-bold text-gray-700 uppercase tracking-wider">{title}</h2>
    </div>
    <div className="w-3/4 pl-2 border-l-2 border-gray-300"> {/* Right column for content */}
      {children}
    </div>
  </div>
);

const CVTemplateForPDF: React.FC<CVTemplateForPDFProps> = ({ cvData, t, getLocalizedDate }) => {
  // Add safety checks for cvData and its properties
  if (!cvData) {
    return <div>No CV data available</div>;
  }

  const {
    personalInformation: pi,
    desiredEmploymentOrPersonalStatement,
    workExperience,
    educationTraining,
    personalSkills,
    drivingLicence,
    additionalInformation,
    attachments
  } = cvData;

  // Ensure personalInformation exists with default values
  const safePersonalInfo = pi || {
    name: '',
    surname: '',
    photo: '',
    address: '',
    telephones: [],
    emailAddresses: [],
    websites: [],
    linkedinProfile: '',
    instantMessaging: [],
    sex: '',
    dateOfBirth: '',
    nationality: ''
  };

  // Ensure personalSkills exists with default values
  const safePersonalSkills = personalSkills || {
    motherTongue: '',
    otherLanguages: [],
    communicationSkills: '',
    organisationalOrManagerialSkills: '',
    professionalSkills: '',
    digitalSkills: '',
    artisticSkills: '',
    otherSkills: ''
  };

  const formatDateRangeEuropass = (startDateStr: string, endDateStr: string | undefined, isCurrent: boolean): string => {
    const formatMonthYear = (dateStr: string) => {
        if (!dateStr) return '';
        // Assuming dateStr is 'YYYY-MM'
        const [year, month] = dateStr.split('-');
        return `${month}/${year}`;
    }
    const startDate = formatMonthYear(startDateStr);
    const endDate = isCurrent ? t('pdf.present') : formatMonthYear(endDateStr || '');
    return `${startDate} – ${endDate}`;
  };

  const renderList = (items?: string[]) => {
    if (!items || items.length === 0) return null;
    return (
      <ul className="list-disc list-inside space-y-0.5">
        {items.map((item, index) => <li key={index}>{item}</li>)}
      </ul>
    );
  }

  const renderInstantMessaging = () => {
    if (!safePersonalInfo.instantMessaging || safePersonalInfo.instantMessaging.length === 0) return null;
    return (
       <div className="mt-1">
        {safePersonalInfo.instantMessaging.map(im => (
          <p key={im.id} className="text-xs">{im.service}: {im.username}</p>
        ))}
      </div>
    );
  }


  return (
    // Simulating A4-ish proportions and Europass style
    // Base font size could be text-xs for ~10pt or text-sm for ~11-12pt
    <div className="font-[Arial, Calibri, sans-serif] text-[10pt] text-gray-900 leading-normal bg-white p-2"> {/* Approx 1.5cm margin with p-8 on A4 */}

      {/* Header - Personal Information (Part 1, without section title) */}
      <div className="flex mb-4">
        {safePersonalInfo.photo && safePersonalInfo.photo.trim() !== '' && (
          <div className="w-1/4 pr-4 flex-shrink-0">
            <img
              src={safePersonalInfo.photo}
              alt={t('pdf.photo')}
              className="w-[80px] h-[auto] object-cover border border-gray-300"
              onError={(e) => {
                // Hide the image container if photo fails to load
                const target = e.target as HTMLImageElement;
                const container = target.parentElement;
                if (container) {
                  container.style.display = 'none';
                }
              }}
            />
          </div>
        )}
        <div className={safePersonalInfo.photo && safePersonalInfo.photo.trim() !== '' ? "w-3/4 pl-2" : "w-full"}>
          <h1 className="text-2xl font-bold text-gray-800">{safePersonalInfo.name} {safePersonalInfo.surname}</h1>
          <div className="text-xs mt-1 space-y-0.5">
            {safePersonalInfo.address && <p><i className="fas fa-map-marker-alt mr-2 text-gray-600"></i>{safePersonalInfo.address}</p>}
            {(safePersonalInfo.telephones && safePersonalInfo.telephones.length > 0) && <p><i className="fas fa-phone-alt mr-2 text-gray-600"></i>{safePersonalInfo.telephones.join(' / ')}</p>}
            {(safePersonalInfo.emailAddresses && safePersonalInfo.emailAddresses.length > 0) && <p><i className="fas fa-envelope mr-2 text-gray-600"></i>{safePersonalInfo.emailAddresses.join(' / ')}</p>}
            {(safePersonalInfo.websites && safePersonalInfo.websites.length > 0) && safePersonalInfo.websites.map((site, idx) => <p key={idx}><i className="fas fa-globe mr-2 text-gray-600"></i><a href={site} className="text-blue-600 hover:underline">{site}</a></p>)}
            {safePersonalInfo.linkedinProfile && <p><i className="fab fa-linkedin mr-2 text-gray-600"></i><a href={safePersonalInfo.linkedinProfile} className="text-blue-600 hover:underline">{safePersonalInfo.linkedinProfile}</a></p>}
            {renderInstantMessaging()}
             <div className="mt-1">
                {safePersonalInfo.sex && <span className="mr-3"><i className="fas fa-venus-mars mr-1 text-gray-600"></i><strong>{t('pdf.sex')}:</strong> {t(`personalInfoSection.sexOptions.${safePersonalInfo.sex.toLowerCase()}`, {defaultValue: safePersonalInfo.sex})}</span>}
                {safePersonalInfo.dateOfBirth && <span className="mr-3"><i className="fas fa-calendar-alt mr-1 text-gray-600"></i><strong>{t('pdf.dateOfBirth')}:</strong> {getLocalizedDate(safePersonalInfo.dateOfBirth, {day: '2-digit', month:'2-digit', year: 'numeric'})}</span>}
                {safePersonalInfo.nationality && <span><i className="fas fa-flag mr-1 text-gray-600"></i><strong>{t('pdf.nationality')}:</strong> {safePersonalInfo.nationality}</span>}
            </div>
          </div>
        </div>
      </div>
      <hr className="my-3 border-gray-300"/>


      {/* Job Applied For / Personal Statement */}
      {desiredEmploymentOrPersonalStatement && (
        <Section title={t('pdf.jobAppliedForTitle')}>
          <p className="whitespace-pre-line text-xs">{desiredEmploymentOrPersonalStatement}</p>
        </Section>
      )}

      {/* Work Experience */}
      {workExperience && workExperience.length > 0 && (
        <Section title={t('pdf.workExperienceTitle')}>
          {workExperience.map((exp: WorkExperienceItem) => (
            <div key={exp.id} className="mb-2.5 last:mb-0">
              <p className="font-bold text-xs">
                {formatDateRangeEuropass(exp.startDate, exp.endDate, exp.current)}
              </p>
              <p className="font-semibold text-xs">{exp.occupationOrPositionHeld}</p>
              <p className="text-xs">{exp.employer}{exp.employerAddress && `, ${exp.employerAddress}`}</p>
              {exp.typeOfBusinessOrSector && <p className="text-xs italic text-gray-600">{exp.typeOfBusinessOrSector}</p>}
              {exp.mainActivitiesAndResponsibilities && (
                <ul className="list-disc list-outside ml-3.5 text-xs mt-0.5 space-y-0.5">
                  {exp.mainActivitiesAndResponsibilities.split('\n').map((line, i) => line.trim() && <li key={i}>{line.trim()}</li>)}
                </ul>
              )}
            </div>
          ))}
        </Section>
      )}

      {/* Education and Training */}
      {educationTraining && educationTraining.length > 0 && (
        <Section title={t('pdf.educationTrainingTitle')}>
          {educationTraining.map((edu: EducationTrainingItem) => (
            <div key={edu.id} className="mb-2.5 last:mb-0">
              <p className="font-bold text-xs">
                {formatDateRangeEuropass(edu.startDate, edu.endDate, edu.current)}
              </p>
              <p className="font-semibold text-xs">{edu.titleOfQualificationAwarded}</p>
              <p className="text-xs">{edu.organisationName}{edu.organisationAddress && `, ${edu.organisationAddress}`}</p>
              {edu.mainSubjectsOrSkillsCovered && (
                 <p className="text-xs mt-0.5"><strong className="text-gray-600">{t('pdf.mainSubjectsOrSkillsCovered')}:</strong> {edu.mainSubjectsOrSkillsCovered}</p>
              )}
              {edu.levelInNationalOrInternationalClassification && <p className="text-xs italic text-gray-600">{t('pdf.levelInClassification')}: {edu.levelInNationalOrInternationalClassification}</p>}
            </div>
          ))}
        </Section>
      )}

      {/* Personal Skills */}
      <Section title={t('pdf.personalSkillsTitle')} className="personal-skills-section">
          <div className="space-y-1.5 text-xs">
            <div>
              <span className="font-semibold">{t('pdf.motherTongue')}:</span> {safePersonalSkills.motherTongue}
            </div>

            {safePersonalSkills.otherLanguages && safePersonalSkills.otherLanguages.length > 0 && (
              <div>
                <h4 className="font-semibold mb-0.5">{t('pdf.otherLanguages')}:</h4>
                <table className="w-full text-[9pt] border-collapse border border-gray-400">
                  <thead>
                    <tr className="bg-gray-100">
                      <th rowSpan={2} className="border border-gray-400 p-1 font-semibold align-middle">{/* Language (empty header) */}</th>
                      <th colSpan={2} className="border border-gray-400 p-1 font-semibold uppercase">{t('pdf.languageTable.understanding')}</th>
                      <th colSpan={2} className="border border-gray-400 p-1 font-semibold uppercase">{t('pdf.languageTable.speaking')}</th>
                      <th rowSpan={2} className="border border-gray-400 p-1 font-semibold align-middle uppercase">{t('pdf.languageTable.writing')}</th>
                    </tr>
                    <tr className="bg-gray-50 text-[8pt]">
                      <th className="border border-gray-400 p-1 font-normal">{t('pdf.languageTable.listening')}</th>
                      <th className="border border-gray-400 p-1 font-normal">{t('pdf.languageTable.reading')}</th>
                      <th className="border border-gray-400 p-1 font-normal">{t('pdf.languageTable.spokenInteraction')}</th>
                      <th className="border border-gray-400 p-1 font-normal">{t('pdf.languageTable.spokenProduction')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {safePersonalSkills.otherLanguages.map((lang: LanguageSkill) => (
                      <tr key={lang.id}>
                        <td className="border border-gray-400 p-1 font-semibold">{lang.language}</td>
                        <td className="border border-gray-400 p-1 text-center">{lang.listening ? t(`skillLevels.${lang.listening}`) : '-'}</td>
                        <td className="border border-gray-400 p-1 text-center">{lang.reading ? t(`skillLevels.${lang.reading}`) : '-'}</td>
                        <td className="border border-gray-400 p-1 text-center">{lang.spokenInteraction ? t(`skillLevels.${lang.spokenInteraction}`) : '-'}</td>
                        <td className="border border-gray-400 p-1 text-center">{lang.spokenProduction ? t(`skillLevels.${lang.spokenProduction}`) : '-'}</td>
                        <td className="border border-gray-400 p-1 text-center">{lang.writing ? t(`skillLevels.${lang.writing}`) : '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {safePersonalSkills.communicationSkills && <div><span className="font-semibold">{t('pdf.communicationSkills')}:</span> <p className="whitespace-pre-line inline">{safePersonalSkills.communicationSkills}</p></div>}
            {safePersonalSkills.organisationalOrManagerialSkills && <div><span className="font-semibold">{t('pdf.organisationalOrManagerialSkills')}:</span> <p className="whitespace-pre-line inline">{safePersonalSkills.organisationalOrManagerialSkills}</p></div>}
            {safePersonalSkills.professionalSkills && <div><span className="font-semibold">{t('pdf.professionalSkills')}:</span> <p className="whitespace-pre-line inline">{safePersonalSkills.professionalSkills}</p></div>}
            {safePersonalSkills.digitalSkills && <div><span className="font-semibold">{t('pdf.digitalSkills')}:</span> <p className="whitespace-pre-line inline">{safePersonalSkills.digitalSkills}</p></div>}
            {safePersonalSkills.artisticSkills && <div><span className="font-semibold">{t('pdf.artisticSkills')}:</span> <p className="whitespace-pre-line inline">{safePersonalSkills.artisticSkills}</p></div>}
            {safePersonalSkills.otherSkills && <div><span className="font-semibold">{t('pdf.otherSkills')}:</span> <p className="whitespace-pre-line inline">{safePersonalSkills.otherSkills}</p></div>}
        </div>
      </Section>

      {/* Driving Licence */}
      {drivingLicence && (
        <Section title={t('pdf.drivingLicenceTitle')}>
          <p className="text-xs">{t('pdf.drivingCategories')}: {drivingLicence}</p>
        </Section>
      )}

      {/* Additional Information */}
      {additionalInformation && (
        <Section title={t('pdf.additionalInformationTitle')}>
          <p className="whitespace-pre-line text-xs">{additionalInformation}</p>
        </Section>
      )}

      {/* Attachments */}
      {attachments && attachments.length > 0 && (
        <Section title={t('pdf.attachmentsTitle')}>
          <ul className="list-disc list-outside ml-3.5 text-xs space-y-0.5">
            {attachments.map((att: Attachment) => (
              <li key={att.id}>
                {att.name}
                {att.description && <span className="italic text-gray-600"> - {att.description}</span>}
              </li>
            ))}
          </ul>
        </Section>
      )}

       {/* Europass Footer - optional, usually includes page number */}
       <div className="text-center mt-6 pt-2 border-t border-gray-300 text-[8pt] text-gray-500">
         {/* Page number would be added by PDF generation library */}
         <p>{cvData.title} - {t('pdf.page', {currentPage: 1, totalPages: 1})}</p> {/* Placeholder for page numbers */}
       </div>

    </div>
  );
};

export default CVTemplateForPDF;
