
import { CVData, WorkExperienceItem, EducationTrainingItem, LanguageSkill, Attachment } from '../types'; // SkillLevelKey not directly used

declare const jspdf: any; // From CDN global jspdf

// Helper function to convert cm to points (jsPDF default unit)
const cmToPt = (cm: number) => cm * 28.3465;

export const generateCVAsPDF = async (
  cvData: CVData,
  t: (key: string, replacements?: Record<string, string | number>) => string,
  getLocalizedDate: (dateString: string, options?: Intl.DateTimeFormatOptions) => string
): Promise<void> => {
  const { jsPDF } = jspdf;
  const doc = new jsPDF({
    orientation: 'p',
    unit: 'pt',
    format: 'a4'
  });

  const pageHeight = doc.internal.pageSize.getHeight();
  const pageWidth = doc.internal.pageSize.getWidth();

  const margin = cmToPt(1.5);
  const leftColWidth = pageWidth * 0.25; // Approx 25% for left column (section titles)
  const rightColX = margin + leftColWidth + cmToPt(0.5); // Start of right column content
  const rightColWidth = pageWidth - rightColX - margin;

  let currentY = margin;
  let currentPage = 1;

  const addPageIfNeeded = (spaceNeeded: number) => {
    if (currentY + spaceNeeded > pageHeight - margin) {
      doc.addPage();
      currentPage++;
      currentY = margin;
      doc.setFontSize(8);
      doc.setTextColor(100);
      doc.text(t('pdf.page', { currentPage, totalPages: '...' }), pageWidth / 2, pageHeight - margin / 2, { align: 'center' }); // Placeholder for total pages
      return true; // Page was added
    }
    return false; // No page added
  };

  const addFooter = () => {
    const tempCurrentPage = doc.internal.getNumberOfPages();
    for (let i = 1; i <= tempCurrentPage; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.setTextColor(100);
        doc.text(t('pdf.page', { currentPage: i, totalPages: tempCurrentPage }), pageWidth / 2, pageHeight - margin / 2, { align: 'center' });
    }
  }

  // Helper for text in right column with auto page break
  const addWrappedTextRightCol = (text: string | string[], options: { fontSize?: number, fontStyle?: 'normal' | 'bold' | 'italic', lineSpacingFactor?: number, isHtml?: boolean } = {}) => {
    if (!text || (Array.isArray(text) && text.length === 0)) return;

    const { fontSize = 9, fontStyle = 'normal', lineSpacingFactor = 1.15 } = options;
    doc.setFontSize(fontSize);
    doc.setFont(undefined, fontStyle);
    doc.setTextColor(50); // Dark grey text

    const lines = doc.splitTextToSize(text, rightColWidth);
    const lineHeight = fontSize * lineSpacingFactor;

    lines.forEach((line: string) => {
      addPageIfNeeded(lineHeight);
      doc.text(line, rightColX, currentY);
      currentY += lineHeight;
    });
    currentY += fontSize * 0.3; // Small gap after text block
  };

  const addSectionTitleLeftCol = (titleKey: string, sectionContentHeightEstimate: number = 20) => {
    addPageIfNeeded(Math.max(20, sectionContentHeightEstimate)); // Ensure title + some content fits or new page
    doc.setFontSize(9);
    doc.setFont(undefined, 'bold');
    doc.setTextColor(80, 80, 80); // Darker grey for section titles

    const titleText = t(titleKey).toUpperCase();
    const titleLines = doc.splitTextToSize(titleText, leftColWidth - cmToPt(0.2)); // Small padding within left col
    let titleY = currentY;
    titleLines.forEach((line: string, index:number) => {
        if (index > 0) titleY += 9 * 1.15;
        doc.text(line, margin, titleY);
    });
    // Draw vertical line separator (or use a border on the right column conceptually)
    // For now, simple text in left column.
  };


  // --- START PDF CONTENT ---

  // 1. Personal Information Header (No explicit section title in left column)
  doc.setFontSize(18);
  doc.setFont(undefined, 'bold');
  doc.setTextColor(0);
  const fullName = `${cvData.personalInformation.name} ${cvData.personalInformation.surname}`;
  // We'll calculate this after determining if photo is valid
  let fullNameLines: string[];

  let photoHeight = 0;
  let hasValidPhoto = false;

  // Check if photo exists and is not empty
  if (cvData.personalInformation.photo && cvData.personalInformation.photo.trim() !== '') {
    try {
        const photoDim = { width: cmToPt(3), height: cmToPt(3) * (4/3) }; // Approx 3cm width, 4:3 aspect
        addPageIfNeeded(photoDim.height + 20); // Space for photo and some text
        doc.addImage(cvData.personalInformation.photo, 'JPEG', margin, currentY, photoDim.width, photoDim.height); // Use JPEG or PNG
        photoHeight = photoDim.height;
        hasValidPhoto = true;
    } catch (e) {
        console.error("Error adding photo to PDF:", e);
        // If photo fails to load, treat as if no photo exists
        hasValidPhoto = false;
        photoHeight = 0;
    }
  }

  let textX = hasValidPhoto ? margin + cmToPt(3) + cmToPt(0.5) : margin;
  let textWidth = pageWidth - textX - margin;

  // Now calculate full name lines based on whether we have a valid photo
  fullNameLines = doc.splitTextToSize(fullName, hasValidPhoto ? pageWidth - margin * 2 - cmToPt(3.5) : pageWidth - margin * 2);

  let headerStartY = currentY;
  fullNameLines.forEach((line: string) => {
    doc.text(line, textX, currentY);
    currentY += 18 * 0.9; // Adjust line height for large font
  });
  currentY += 5; // Space after name

  doc.setFontSize(8);
  doc.setFont(undefined, 'normal');
  const contactInfo: string[] = [];
  if(cvData.personalInformation.address) contactInfo.push(`\uf041 ${cvData.personalInformation.address}`); // FontAwesome icons need special handling in jsPDF (not done here)
  if(cvData.personalInformation.telephones?.length) contactInfo.push(`\uf095 ${cvData.personalInformation.telephones.join(' / ')}`);
  if(cvData.personalInformation.emailAddresses?.length) contactInfo.push(`\uf0e0 ${cvData.personalInformation.emailAddresses.join(' / ')}`);
  if(cvData.personalInformation.websites?.length) cvData.personalInformation.websites.forEach(w => contactInfo.push(`\uf0ac ${w}`));
  if(cvData.personalInformation.linkedinProfile) contactInfo.push(`\uf0e1 ${cvData.personalInformation.linkedinProfile}`);

  contactInfo.forEach(line => {
    const lines = doc.splitTextToSize(line, textWidth);
    lines.forEach((l: string) => {
        if (currentY > headerStartY + photoHeight && photoHeight > 0) { // If text goes below photo, reset X
            textX = margin;
            textWidth = pageWidth - margin * 2;
        }
        if (currentY + 8 > pageHeight - margin && (headerStartY + photoHeight) - currentY < 20) addPageIfNeeded(20); // Check if new page needed
        doc.text(l, textX, currentY);
        currentY += 8 * 1.15;
    });
  });

  const personalDetails: string[] = [];
  if(cvData.personalInformation.sex) personalDetails.push(`${t('pdf.sex')}: ${t(`personalInfoSection.sexOptions.${cvData.personalInformation.sex.toLowerCase()}`, {defaultValue: cvData.personalInformation.sex})}`);
  if(cvData.personalInformation.dateOfBirth) personalDetails.push(`${t('pdf.dateOfBirth')}: ${getLocalizedDate(cvData.personalInformation.dateOfBirth, {day:'2-digit', month:'2-digit', year:'numeric'})}`);
  if(cvData.personalInformation.nationality) personalDetails.push(`${t('pdf.nationality')}: ${cvData.personalInformation.nationality}`);

  if (personalDetails.length > 0) {
    currentY += 3;
    const pdLine = personalDetails.join(' | ');
    const lines = doc.splitTextToSize(pdLine, textWidth);
     lines.forEach((l: string) => {
        if (currentY > headerStartY + photoHeight && photoHeight > 0) {
             textX = margin; textWidth = pageWidth - margin*2;
        }
        if (currentY + 8 > pageHeight - margin && (headerStartY + photoHeight) - currentY < 20) addPageIfNeeded(20);
        doc.text(l, textX, currentY);
        currentY += 8 * 1.15;
    });
  }

  // Ensure currentY is at least below photo if photo was taller
  currentY = Math.max(currentY, headerStartY + photoHeight + 10);
  currentY += 10; // Extra space before first section
  doc.setLineWidth(0.5);
  doc.line(margin, currentY - 5, pageWidth - margin, currentY - 5); // Horizontal line

  // --- Sections with Left Title Column ---

  const renderSection = (titleKey: string, contentRenderer: () => void) => {
    const initialY = currentY;
    addPageIfNeeded(30); // Minimum space for section title + a bit of content
    addSectionTitleLeftCol(titleKey);
    const sectionTitleEndY = currentY; // Y after title text in left column
    currentY = initialY; // Reset Y for content in right column, aligned with top of title
    contentRenderer();
    currentY = Math.max(currentY, sectionTitleEndY); // Ensure Y is below both title and content
    currentY += 15; // Space after section
  };

  // 2. Job Applied For / Personal Statement
  if (cvData.desiredEmploymentOrPersonalStatement) {
    renderSection('pdf.jobAppliedForTitle', () => {
      addWrappedTextRightCol(cvData.desiredEmploymentOrPersonalStatement!);
    });
  }

  // 3. Work Experience
  if (cvData.workExperience?.length) {
    renderSection('pdf.workExperienceTitle', () => {
      cvData.workExperience.forEach((exp: WorkExperienceItem) => {
        const expStartY = currentY;
        const dateRange = `${exp.startDate ? exp.startDate.replace('-', '/') : ''} – ${exp.current ? t('pdf.present') : (exp.endDate ? exp.endDate.replace('-', '/') : '')}`;
        addWrappedTextRightCol(dateRange, { fontStyle: 'bold' });
        addWrappedTextRightCol(exp.occupationOrPositionHeld, { fontStyle: 'bold', fontSize: 10 });
        addWrappedTextRightCol(`${exp.employer}${exp.employerAddress ? `, ${exp.employerAddress}` : ''}`);
        if(exp.typeOfBusinessOrSector) addWrappedTextRightCol(exp.typeOfBusinessOrSector, { fontStyle: 'italic', fontSize: 8 });

        if (exp.mainActivitiesAndResponsibilities) {
            const activities = exp.mainActivitiesAndResponsibilities.split('\n').map(act => `• ${act.trim()}`).join('\n');
            addWrappedTextRightCol(activities);
        }
        currentY += 5; // Space between experiences
        if (addPageIfNeeded(0) && currentY < pageHeight - margin - 100) { // If page broke, re-add title to left col
             addSectionTitleLeftCol('pdf.workExperienceTitle');
             currentY = Math.max(currentY, expStartY); // Try to align with where it broke
        }
      });
    });
  }

  // 4. Education and Training
  if (cvData.educationTraining?.length) {
    renderSection('pdf.educationTrainingTitle', () => {
      cvData.educationTraining.forEach((edu: EducationTrainingItem) => {
        const eduStartY = currentY;
        const dateRange = `${edu.startDate ? edu.startDate.replace('-', '/') : ''} – ${edu.current ? t('pdf.present') : (edu.endDate ? edu.endDate.replace('-', '/') : '')}`;
        addWrappedTextRightCol(dateRange, { fontStyle: 'bold' });
        addWrappedTextRightCol(edu.titleOfQualificationAwarded, { fontStyle: 'bold', fontSize: 10 });
        addWrappedTextRightCol(`${edu.organisationName}${edu.organisationAddress ? `, ${edu.organisationAddress}` : ''}`);
        if(edu.mainSubjectsOrSkillsCovered) addWrappedTextRightCol(`${t('pdf.mainSubjectsOrSkillsCovered')}: ${edu.mainSubjectsOrSkillsCovered}`);
        if(edu.levelInNationalOrInternationalClassification) addWrappedTextRightCol(`${t('pdf.levelInClassification')}: ${edu.levelInNationalOrInternationalClassification}`, { fontStyle: 'italic', fontSize: 8 });
        currentY += 5;
         if (addPageIfNeeded(0) && currentY < pageHeight - margin - 100) {
             addSectionTitleLeftCol('pdf.educationTrainingTitle');
             currentY = Math.max(currentY, eduStartY);
        }
      });
    });
  }

  // 5. Personal Skills
  renderSection('pdf.personalSkillsTitle', () => {
    addWrappedTextRightCol(`${t('pdf.motherTongue')}: ${cvData.personalSkills.motherTongue}`, { fontStyle: 'bold' });
    currentY += 5;

    if (cvData.personalSkills.otherLanguages?.length) {
      addWrappedTextRightCol(`${t('pdf.otherLanguages')}:`, { fontStyle: 'bold' });
      const tableStartY = currentY;
      const head = [[
        '', // Language name column header
        { content: t('pdf.languageTable.understanding').toUpperCase(), colSpan: 2, styles: { halign: 'center' } },
        { content: t('pdf.languageTable.speaking').toUpperCase(), colSpan: 2, styles: { halign: 'center' } },
        { content: t('pdf.languageTable.writing').toUpperCase(), rowSpan: 2, styles: { halign: 'center', valign: 'middle' } }
      ],[
        '', // Second row for language name
        t('pdf.languageTable.listening'), t('pdf.languageTable.reading'),
        t('pdf.languageTable.spokenInteraction'), t('pdf.languageTable.spokenProduction')
      ]];

      const body = cvData.personalSkills.otherLanguages.map((lang: LanguageSkill) => [
        lang.language,
        lang.listening ? t(`skillLevels.${lang.listening}`) : '-',
        lang.reading ? t(`skillLevels.${lang.reading}`) : '-',
        lang.spokenInteraction ? t(`skillLevels.${lang.spokenInteraction}`) : '-',
        lang.spokenProduction ? t(`skillLevels.${lang.spokenProduction}`) : '-',
        lang.writing ? t(`skillLevels.${lang.writing}`) : '-',
      ]);

      (doc as any).autoTable({
        head,
        body,
        startY: currentY,
        theme: 'grid',
        styles: { fontSize: 7, cellPadding: 1.5, lineColor: [200,200,200], lineWidth: 0.5 },
        headStyles: { fillColor: [230, 230, 230], textColor: 0, fontStyle: 'bold', fontSize: 6.5, cellPadding:1.5 },
        columnStyles: { 0: { fontStyle: 'bold' } },
        margin: { left: rightColX },
        tableWidth: rightColWidth,
        didDrawPage: (data: any) => {
            currentY = data.cursor.y + 5; // Update Y after table draws (potentially on new page)
            if(data.pageNumber > currentPage) { // if autotable added a page
                 currentPage = data.pageNumber;
                 // Re-draw section title on new page if table spanned
                 doc.setFontSize(9); doc.setFont(undefined, 'bold'); doc.setTextColor(80,80,80);
                 const titleText = t('pdf.personalSkillsTitle').toUpperCase();
                 const titleLines = doc.splitTextToSize(titleText, leftColWidth - cmToPt(0.2));
                 let titleY = margin;
                 titleLines.forEach((line: string, index:number) => {
                    if (index > 0) titleY += 9 * 1.15;
                    doc.text(line, margin, titleY);
                 });
            }
        },
        pageBreak: 'auto'
      });
      currentY = (doc as any).lastAutoTable.finalY + 10;
    }

    const skillsMap = [
        { key: 'communicationSkills', labelKey: 'pdf.communicationSkills' },
        { key: 'organisationalOrManagerialSkills', labelKey: 'pdf.organisationalOrManagerialSkills' },
        { key: 'professionalSkills', labelKey: 'pdf.professionalSkills' },
        { key: 'digitalSkills', labelKey: 'pdf.digitalSkills' },
        { key: 'artisticSkills', labelKey: 'pdf.artisticSkills' },
        { key: 'otherSkills', labelKey: 'pdf.otherSkills' },
    ];

    skillsMap.forEach(skill => {
        const skillText = cvData.personalSkills[skill.key as keyof typeof cvData.personalSkills] as string | undefined;
        if (skillText) {
            addWrappedTextRightCol(`${t(skill.labelKey)}:`, { fontStyle: 'bold' });
            addWrappedTextRightCol(skillText);
            currentY +=3;
        }
    });
  });

  // 6. Driving Licence
  if (cvData.drivingLicence) {
    renderSection('pdf.drivingLicenceTitle', () => {
      addWrappedTextRightCol(`${t('pdf.drivingCategories')}: ${cvData.drivingLicence}`);
    });
  }

  // 7. Additional Information
  if (cvData.additionalInformation) {
    renderSection('pdf.additionalInformationTitle', () => {
      addWrappedTextRightCol(cvData.additionalInformation!);
    });
  }

  // 8. Attachments
  if (cvData.attachments?.length) {
    renderSection('pdf.attachmentsTitle', () => {
      cvData.attachments.forEach((att: Attachment) => {
        addWrappedTextRightCol(att.name, {fontStyle: 'bold'});
        if(att.description) addWrappedTextRightCol(att.description, {fontSize: 8, fontStyle:'italic'});
        currentY += 3;
      });
    });
  }

  addFooter(); // Add footers to all pages

  const safeCvTitle = cvData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
  doc.save(`${safeCvTitle || 'europass_cv'}_${new Date().toISOString().split('T')[0]}.pdf`);
};

// generateCVWithHtml2Canvas is less suitable for strict Europass two-column if HTML isn't perfectly styled.
// For now, focusing on the jsPDF direct generation.
export const generateCVWithHtml2Canvas = async (elementId: string, cvName: string): Promise<void> => {
    // ... (implementation would remain similar, but its output might not be perfectly Europass compliant)
    console.warn("generateCVWithHtml2Canvas may not produce a PDF that is strictly Europass compliant in layout compared to direct jsPDF generation.")
    // For this update, we focus on generateCVAsPDF.
};
